#!/usr/bin/env python3
###################################################################
# Copyright (C) 2025 Vita Dynamics. All rights reserved.
#
# Filename: smart_velocity_commander.py
# Author : AI Assistant
# Date : Jan, 2025
# Describe: 智能速度控制器，带冲突检测
###################################################################

import argparse
import signal
import sys
import time
import threading

import rclpy
from rclpy.node import Node
from geometry_msgs.msg import Twist
from sensor_msgs.msg import Joy


class SmartVelocityCommander(Node):
    """智能速度指令发送器，带冲突检测"""
    
    def __init__(self):
        super().__init__('smart_velocity_commander')
        
        # 状态变量
        self.external_joy_active = False
        self.last_joy_time = 0.0
        self.joy_timeout = 1.0  # 1秒超时
        self.is_publishing = False
        
        # 创建发布者
        self.cmd_publisher = self.create_publisher(Twist, '/vel_cmd', 10)
        
        # 创建订阅者监听外部joy
        self.joy_subscriber = self.create_subscription(
            Joy, '/joy', self.joy_callback, 10
        )
        
        # 监听现有的joy_cmd话题
        self.joy_cmd_subscriber = self.create_subscription(
            Twist, '/joy_cmd', self.joy_cmd_callback, 10
        )
        
        # 创建定时器检查joy状态
        self.check_timer = self.create_timer(0.1, self.check_joy_status)
        
        self.get_logger().info("Smart Velocity Commander initialized")
        
    def joy_callback(self, msg: Joy):
        """处理外部joy消息"""
        current_time = time.time()
        
        # 检查是否有有效输入
        has_input = False
        if len(msg.axes) >= 4:
            lx, ly, rx, ry = msg.axes[0], msg.axes[1], msg.axes[3], msg.axes[4]
            if abs(lx) > 0.1 or abs(ly) > 0.1 or abs(rx) > 0.1 or abs(ry) > 0.1:
                has_input = True
        
        if len(msg.buttons) > 0 and any(button > 0 for button in msg.buttons):
            has_input = True
            
        if has_input:
            self.last_joy_time = current_time
            if not self.external_joy_active:
                self.external_joy_active = True
                self.get_logger().warn("External joy detected! Pausing script commands...")
                
    def joy_cmd_callback(self, msg: Twist):
        """监听joy_cmd话题"""
        if (abs(msg.linear.x) > 0.01 or abs(msg.linear.y) > 0.01 or 
            abs(msg.angular.z) > 0.01):
            current_time = time.time()
            self.last_joy_time = current_time
            if not self.external_joy_active:
                self.external_joy_active = True
                self.get_logger().warn("JoyNode activity detected! Pausing script commands...")
    
    def check_joy_status(self):
        """检查joy状态"""
        current_time = time.time()
        
        if (self.external_joy_active and 
            current_time - self.last_joy_time > self.joy_timeout):
            self.external_joy_active = False
            self.get_logger().info("External joy timeout. Ready to resume script commands.")
    
    def send_velocity_smart(self, linear_x: float, linear_y: float, angular_z: float, 
                           duration: float, rate: float = 20.0):
        """智能发送速度指令"""
        
        # 创建速度消息
        cmd = Twist()
        cmd.linear.x = linear_x
        cmd.linear.y = linear_y
        cmd.linear.z = 0.0
        cmd.angular.x = 0.0
        cmd.angular.y = 0.0
        cmd.angular.z = angular_z
        
        period = 1.0 / rate
        start_time = time.time()
        
        self.get_logger().info(
            f"Starting smart velocity control: linear=({linear_x:.2f}, {linear_y:.2f}), "
            f"angular={angular_z:.2f} for {duration:.1f}s"
        )
        
        self.is_publishing = True
        
        while time.time() - start_time < duration and rclpy.ok() and self.is_publishing:
            # 检查是否有外部joy冲突
            if self.external_joy_active:
                self.get_logger().warn("Pausing due to external joy activity...")
                # 暂停但不退出，等待joy停止
                while self.external_joy_active and rclpy.ok() and self.is_publishing:
                    time.sleep(0.1)
                    rclpy.spin_once(self, timeout_sec=0.0)
                
                if self.external_joy_active or not self.is_publishing:
                    break
                    
                self.get_logger().info("Resuming script commands...")
            
            # 发布命令
            self.cmd_publisher.publish(cmd)
            
            # 处理ROS回调
            rclpy.spin_once(self, timeout_sec=0.0)
            
            time.sleep(period)
            
            # 每秒打印进度
            elapsed = time.time() - start_time
            if int(elapsed) % 1 == 0 and elapsed > 0:
                remaining = duration - elapsed
                if remaining > 0:
                    self.get_logger().info(f"Remaining: {remaining:.1f}s")
        
        # 发送停止指令
        stop_cmd = Twist()
        self.cmd_publisher.publish(stop_cmd)
        self.get_logger().info("Smart velocity control completed")
        self.is_publishing = False
    
    def stop(self):
        """停止发布"""
        self.is_publishing = False
        stop_cmd = Twist()
        self.cmd_publisher.publish(stop_cmd)
        self.get_logger().info("Smart velocity control stopped")


def signal_handler(signum, frame):
    """信号处理器"""
    print(f"\nReceived signal {signum}, stopping...")
    if 'commander' in globals():
        commander.stop()
    rclpy.shutdown()
    sys.exit(0)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Smart Velocity Commander with Conflict Detection')
    parser.add_argument('--velocity', type=str,
                       help='Velocity in format "x,y,z" (linear_x,linear_y,angular_z)')
    parser.add_argument('--linear-x', type=float, default=0.0,
                       help='Linear velocity in x direction (m/s)')
    parser.add_argument('--linear-y', type=float, default=0.0,
                       help='Linear velocity in y direction (m/s)')
    parser.add_argument('--angular-z', type=float, default=0.0,
                       help='Angular velocity around z axis (rad/s)')
    parser.add_argument('--duration', type=float, required=True,
                       help='Duration to send commands in seconds')
    parser.add_argument('--rate', type=float, default=20.0,
                       help='Publishing rate in Hz (default: 20.0)')

    args = parser.parse_args()

    # 解析速度参数
    if args.velocity:
        try:
            velocity_parts = args.velocity.split(',')
            if len(velocity_parts) != 3:
                raise ValueError("Velocity must have 3 components")

            linear_x = float(velocity_parts[0])
            linear_y = float(velocity_parts[1])
            angular_z = float(velocity_parts[2])

        except (ValueError, IndexError) as e:
            print(f"Error parsing velocity: {e}")
            print("Example: --velocity 0.5,0.0,0.2")
            sys.exit(1)
    else:
        linear_x = args.linear_x
        linear_y = args.linear_y
        angular_z = args.angular_z
    
    # 设置信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    global commander
    
    try:
        # 初始化ROS2
        rclpy.init()
        
        # 创建节点
        commander = SmartVelocityCommander()
        
        # 等待一下让订阅者连接
        time.sleep(1.0)
        
        # 发送智能速度指令
        commander.send_velocity_smart(linear_x, linear_y, angular_z, args.duration, args.rate)
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        if rclpy.ok():
            rclpy.shutdown()


if __name__ == '__main__':
    main()
